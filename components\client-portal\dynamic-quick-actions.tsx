"use client"

import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { useCarouselData } from "@/lib/carousel-storage"
import {
  Calendar,
  Scissors,
  ShoppingBag,
  Gift,
  Star,
  Package,
  User,
  Heart,
  Phone,
  Mail,
  MapPin,
  Clock
} from "lucide-react"

// Icon mapping for dynamic icons
const iconMap = {
  Calendar,
  Scissors,
  ShoppingBag,
  Gift,
  Star,
  Package,
  User,
  Heart,
  Phone,
  Mail,
  MapPin,
  Clock
}

export function DynamicQuickActions() {
  const { quickActions } = useCarouselData()

  // Filter active quick actions and sort by order
  const activeQuickActions = quickActions
    .filter(action => action.isActive)
    .sort((a, b) => a.order - b.order)

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
      {activeQuickActions.map((action) => {
        const IconComponent = iconMap[action.icon as keyof typeof iconMap] || Calendar

        return (
          <Link key={action.id} href={action.href}>
            <Card className="hover:shadow-md transition-shadow cursor-pointer group">
              <CardContent className="p-4 flex flex-col items-center text-center">
                <div className="w-12 h-12 rounded-full bg-pink-100 flex items-center justify-center text-pink-600 mb-3 group-hover:bg-pink-200 transition-colors">
                  <IconComponent className="h-6 w-6" />
                </div>
                <h3 className="font-medium group-hover:text-pink-600 transition-colors">
                  {action.title}
                </h3>
                <p className="text-sm text-gray-500">{action.description}</p>
              </CardContent>
            </Card>
          </Link>
        )
      })}
    </div>
  )
}
