import { Transaction } from "@/lib/transaction-types"
import { format } from "date-fns"

// PDF export utility using jsPDF (will be installed as dependency)
export async function exportTransactionToPDF(transaction: Transaction) {
  try {
    // Dynamic import to avoid SSR issues
    const { jsPDF } = await import('jspdf')
    
    const doc = new jsPDF()
    
    // Helper function to get location details
    const getLocationDetails = (locationId: string) => {
      switch (locationId) {
        case "loc1":
          return {
            name: "Vanity Hair & Beauty - D-Ring Road",
            address: "D-Ring Road, Doha, Qatar",
            phone: "+974 4444 5555",
            email: "<EMAIL>"
          }
        case "loc2":
          return {
            name: "Vanity Hair & Beauty - Muaither",
            address: "Muaither, Doha, Qatar", 
            phone: "+974 4444 6666",
            email: "<EMAIL>"
          }
        case "loc3":
          return {
            name: "Vanity Hair & Beauty - Medinat Khalifa",
            address: "Medinat Khalifa, Doha, Qatar",
            phone: "+974 4444 7777", 
            email: "<EMAIL>"
          }
        default:
          return {
            name: "Vanity Hair & Beauty",
            address: "Doha, Qatar",
            phone: "+974 4444 5555",
            email: "<EMAIL>"
          }
      }
    }

    const location = getLocationDetails(transaction.location)
    
    // Helper function to format payment method
    const formatPaymentMethod = (method: string) => {
      switch (method) {
        case "credit_card":
          return "Credit Card"
        case "mobile_payment":
          return "Mobile Payment"
        case "bank_transfer":
          return "Bank Transfer"
        case "cash":
          return "Cash"
        default:
          return method.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
      }
    }

    // Set up the document
    let yPosition = 20
    const pageWidth = doc.internal.pageSize.width
    const margin = 20

    // Header
    doc.setFontSize(18)
    doc.setFont('helvetica', 'bold')
    doc.text(location.name, pageWidth / 2, yPosition, { align: 'center' })
    yPosition += 10

    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')
    doc.text(location.address, pageWidth / 2, yPosition, { align: 'center' })
    yPosition += 6
    doc.text(`Tel: ${location.phone} | Email: ${location.email}`, pageWidth / 2, yPosition, { align: 'center' })
    yPosition += 15

    // Transaction Receipt Title
    doc.setFontSize(16)
    doc.setFont('helvetica', 'bold')
    doc.text('TRANSACTION RECEIPT', pageWidth / 2, yPosition, { align: 'center' })
    yPosition += 15

    // Transaction Details
    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')
    
    const leftCol = margin
    const rightCol = pageWidth - margin
    
    // Transaction ID
    doc.text('Transaction ID:', leftCol, yPosition)
    doc.text(transaction.id, rightCol, yPosition, { align: 'right' })
    yPosition += 6

    // Date
    doc.text('Date:', leftCol, yPosition)
    const dateStr = typeof transaction.date === 'string' 
      ? transaction.date 
      : format(transaction.date, 'dd/MM/yyyy HH:mm')
    doc.text(dateStr, rightCol, yPosition, { align: 'right' })
    yPosition += 6

    // Client
    if (transaction.clientName) {
      doc.text('Client:', leftCol, yPosition)
      doc.text(transaction.clientName, rightCol, yPosition, { align: 'right' })
      yPosition += 6
    }

    // Staff
    if (transaction.staffName) {
      doc.text('Staff:', leftCol, yPosition)
      doc.text(transaction.staffName, rightCol, yPosition, { align: 'right' })
      yPosition += 6
    }

    // Payment Method
    doc.text('Payment Method:', leftCol, yPosition)
    doc.text(formatPaymentMethod(transaction.paymentMethod), rightCol, yPosition, { align: 'right' })
    yPosition += 6

    // Status
    doc.text('Status:', leftCol, yPosition)
    doc.text(transaction.status.toUpperCase(), rightCol, yPosition, { align: 'right' })
    yPosition += 15

    // Items Section
    doc.setFont('helvetica', 'bold')
    doc.text('ITEMS:', leftCol, yPosition)
    yPosition += 8

    doc.setFont('helvetica', 'normal')
    
    if (transaction.items && transaction.items.length > 0) {
      // Table headers
      doc.setFont('helvetica', 'bold')
      doc.text('Item', leftCol, yPosition)
      doc.text('Qty', leftCol + 80, yPosition)
      doc.text('Unit Price', leftCol + 110, yPosition)
      doc.text('Total', rightCol, yPosition, { align: 'right' })
      yPosition += 6

      // Draw line under headers
      doc.line(leftCol, yPosition, rightCol, yPosition)
      yPosition += 6

      doc.setFont('helvetica', 'normal')
      
      // Items
      transaction.items.forEach((item) => {
        doc.text(item.name, leftCol, yPosition)
        doc.text(item.quantity.toString(), leftCol + 80, yPosition)
        doc.text(`QAR ${item.unitPrice.toFixed(2)}`, leftCol + 110, yPosition)
        doc.text(`QAR ${item.totalPrice.toFixed(2)}`, rightCol, yPosition, { align: 'right' })
        yPosition += 6
      })
    } else {
      doc.text(transaction.description, leftCol, yPosition)
      yPosition += 6
    }

    yPosition += 10

    // Totals
    doc.line(leftCol, yPosition, rightCol, yPosition)
    yPosition += 8

    doc.text('Subtotal:', leftCol + 100, yPosition)
    doc.text(`QAR ${transaction.amount.toFixed(2)}`, rightCol, yPosition, { align: 'right' })
    yPosition += 6

    doc.text('Tax:', leftCol + 100, yPosition)
    doc.text('QAR 0.00', rightCol, yPosition, { align: 'right' })
    yPosition += 6

    // Total line
    doc.line(leftCol + 100, yPosition, rightCol, yPosition)
    yPosition += 6

    doc.setFont('helvetica', 'bold')
    doc.setFontSize(12)
    doc.text('TOTAL:', leftCol + 100, yPosition)
    doc.text(`QAR ${transaction.amount.toFixed(2)}`, rightCol, yPosition, { align: 'right' })
    yPosition += 15

    // Additional Information
    if (transaction.metadata) {
      doc.setFontSize(10)
      doc.setFont('helvetica', 'bold')
      doc.text('ADDITIONAL INFORMATION:', leftCol, yPosition)
      yPosition += 8

      doc.setFont('helvetica', 'normal')
      
      if (transaction.metadata.appointmentId) {
        doc.text('Appointment ID:', leftCol, yPosition)
        doc.text(transaction.metadata.appointmentId, rightCol, yPosition, { align: 'right' })
        yPosition += 6
      }

      if (transaction.metadata.bookingReference) {
        doc.text('Booking Reference:', leftCol, yPosition)
        doc.text(transaction.metadata.bookingReference, rightCol, yPosition, { align: 'right' })
        yPosition += 6
      }

      if (transaction.metadata.completedAt) {
        doc.text('Completed At:', leftCol, yPosition)
        doc.text(format(new Date(transaction.metadata.completedAt), 'dd/MM/yyyy HH:mm'), rightCol, yPosition, { align: 'right' })
        yPosition += 6
      }
    }

    // Footer
    yPosition = doc.internal.pageSize.height - 40
    doc.setFontSize(8)
    doc.setFont('helvetica', 'normal')
    doc.text('Thank you for choosing Vanity Hair & Beauty!', pageWidth / 2, yPosition, { align: 'center' })
    yPosition += 4
    doc.text('Follow us @VanityQatar | www.vanitysalon.qa', pageWidth / 2, yPosition, { align: 'center' })
    yPosition += 8
    doc.text(`Generated on ${format(new Date(), 'dd/MM/yyyy HH:mm')}`, pageWidth / 2, yPosition, { align: 'center' })

    // Save the PDF
    const fileName = `transaction-${transaction.id}-${format(new Date(), 'yyyyMMdd-HHmm')}.pdf`
    doc.save(fileName)

    return true
  } catch (error) {
    console.error('Error generating PDF:', error)
    throw new Error('Failed to generate PDF')
  }
}

// Alternative HTML-to-PDF export using browser's print functionality
export function exportTransactionToHTMLPDF(transaction: Transaction) {
  const getLocationDetails = (locationId: string) => {
    switch (locationId) {
      case "loc1":
        return {
          name: "Vanity Hair & Beauty - D-Ring Road",
          address: "D-Ring Road, Doha, Qatar",
          phone: "+974 4444 5555",
          email: "<EMAIL>"
        }
      case "loc2":
        return {
          name: "Vanity Hair & Beauty - Muaither",
          address: "Muaither, Doha, Qatar", 
          phone: "+974 4444 6666",
          email: "<EMAIL>"
        }
      case "loc3":
        return {
          name: "Vanity Hair & Beauty - Medinat Khalifa",
          address: "Medinat Khalifa, Doha, Qatar",
          phone: "+974 4444 7777", 
          email: "<EMAIL>"
        }
      default:
        return {
          name: "Vanity Hair & Beauty",
          address: "Doha, Qatar",
          phone: "+974 4444 5555",
          email: "<EMAIL>"
        }
    }
  }

  const location = getLocationDetails(transaction.location)
  
  const formatPaymentMethod = (method: string) => {
    switch (method) {
      case "credit_card":
        return "Credit Card"
      case "mobile_payment":
        return "Mobile Payment"
      case "bank_transfer":
        return "Bank Transfer"
      case "cash":
        return "Cash"
      default:
        return method.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    }
  }

  const printWindow = window.open('', '_blank')
  if (!printWindow) {
    alert('Please allow popups to export PDF')
    return
  }

  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Transaction ${transaction.id}</title>
      <style>
        body { 
          font-family: Arial, sans-serif; 
          margin: 0; 
          padding: 20px; 
          font-size: 12px;
          line-height: 1.4;
        }
        .header { text-align: center; margin-bottom: 30px; }
        .company-name { font-size: 18px; font-weight: bold; margin-bottom: 5px; }
        .company-info { font-size: 10px; color: #666; }
        .title { font-size: 16px; font-weight: bold; text-align: center; margin: 20px 0; }
        .info-row { display: flex; justify-content: space-between; margin-bottom: 5px; }
        .section { margin: 20px 0; }
        .section-title { font-weight: bold; margin-bottom: 10px; }
        .items-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .items-table th, .items-table td { 
          padding: 8px; 
          text-align: left; 
          border-bottom: 1px solid #ddd; 
        }
        .items-table th { font-weight: bold; background-color: #f5f5f5; }
        .total-section { margin-top: 20px; }
        .total-line { border-top: 2px solid #000; padding-top: 5px; font-weight: bold; }
        .footer { text-align: center; margin-top: 40px; font-size: 10px; color: #666; }
        @media print {
          body { margin: 0; padding: 15px; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="company-name">${location.name}</div>
        <div class="company-info">${location.address}</div>
        <div class="company-info">Tel: ${location.phone} | Email: ${location.email}</div>
      </div>

      <div class="title">TRANSACTION RECEIPT</div>

      <div class="section">
        <div class="info-row">
          <span>Transaction ID:</span>
          <span>${transaction.id}</span>
        </div>
        <div class="info-row">
          <span>Date:</span>
          <span>${typeof transaction.date === 'string' ? transaction.date : format(transaction.date, 'dd/MM/yyyy HH:mm')}</span>
        </div>
        ${transaction.clientName ? `
        <div class="info-row">
          <span>Client:</span>
          <span>${transaction.clientName}</span>
        </div>` : ''}
        ${transaction.staffName ? `
        <div class="info-row">
          <span>Staff:</span>
          <span>${transaction.staffName}</span>
        </div>` : ''}
        <div class="info-row">
          <span>Payment Method:</span>
          <span>${formatPaymentMethod(transaction.paymentMethod)}</span>
        </div>
        <div class="info-row">
          <span>Status:</span>
          <span>${transaction.status.toUpperCase()}</span>
        </div>
      </div>

      <div class="section">
        <div class="section-title">ITEMS</div>
        ${transaction.items && transaction.items.length > 0 ? `
        <table class="items-table">
          <thead>
            <tr>
              <th>Item</th>
              <th>Qty</th>
              <th>Unit Price</th>
              <th>Total</th>
            </tr>
          </thead>
          <tbody>
            ${transaction.items.map(item => `
            <tr>
              <td>${item.name}</td>
              <td>${item.quantity}</td>
              <td>QAR ${item.unitPrice.toFixed(2)}</td>
              <td>QAR ${item.totalPrice.toFixed(2)}</td>
            </tr>
            `).join('')}
          </tbody>
        </table>
        ` : `
        <div>${transaction.description}</div>
        `}
      </div>

      <div class="total-section">
        <div class="info-row">
          <span>Subtotal:</span>
          <span>QAR ${transaction.amount.toFixed(2)}</span>
        </div>
        <div class="info-row">
          <span>Tax:</span>
          <span>QAR 0.00</span>
        </div>
        <div class="info-row total-line">
          <span>TOTAL:</span>
          <span>QAR ${transaction.amount.toFixed(2)}</span>
        </div>
      </div>

      ${transaction.metadata ? `
      <div class="section">
        <div class="section-title">ADDITIONAL INFORMATION</div>
        ${transaction.metadata.appointmentId ? `
        <div class="info-row">
          <span>Appointment ID:</span>
          <span>${transaction.metadata.appointmentId}</span>
        </div>` : ''}
        ${transaction.metadata.bookingReference ? `
        <div class="info-row">
          <span>Booking Reference:</span>
          <span>${transaction.metadata.bookingReference}</span>
        </div>` : ''}
        ${transaction.metadata.completedAt ? `
        <div class="info-row">
          <span>Completed At:</span>
          <span>${format(new Date(transaction.metadata.completedAt), 'dd/MM/yyyy HH:mm')}</span>
        </div>` : ''}
      </div>` : ''}

      <div class="footer">
        <div>Thank you for choosing Vanity Hair & Beauty!</div>
        <div>Follow us @VanityQatar | www.vanitysalon.qa</div>
        <div style="margin-top: 10px;">Generated on ${format(new Date(), 'dd/MM/yyyy HH:mm')}</div>
      </div>

      <script>
        window.onload = function() {
          setTimeout(() => {
            window.print();
          }, 500);
        }
      </script>
    </body>
    </html>
  `

  printWindow.document.write(htmlContent)
  printWindow.document.close()
}
