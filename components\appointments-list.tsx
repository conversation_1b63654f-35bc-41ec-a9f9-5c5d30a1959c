"use client"

import { useAuth } from "@/lib/auth-provider"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { getAllAppointments } from "@/lib/appointment-service"

interface AppointmentsListProps {
  date?: Date
  onlyMine?: boolean
}

export function AppointmentsList({ date, onlyMine = false }: AppointmentsListProps) {
  const { user, currentLocation } = useAuth()

  // Get all REAL appointments from appointment service - NO mock data
  const allAppointments = getAllAppointments();

  // Filter REAL appointments based on location, date, and staff
  const filteredAppointments = allAppointments.filter((appointment) => {
    // Filter by location
    if (currentLocation !== "all" && appointment.location !== currentLocation) {
      return false
    }

    // Filter by date if provided
    if (date) {
      const appointmentDate = new Date(appointment.date)
      if (
        appointmentDate.getDate() !== date.getDate() ||
        appointmentDate.getMonth() !== date.getMonth() ||
        appointmentDate.getFullYear() !== date.getFullYear()
      ) {
        return false
      }
    }

    // Filter by staff if onlyMine is true
    if (onlyMine && appointment.staffId !== user?.id) {
      return false
    }

    return true
  })

  if (filteredAppointments.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center text-muted-foreground">No appointments found</CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {filteredAppointments.map((appointment) => (
        <Card key={appointment.id}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Avatar className="h-10 w-10">
                  <AvatarFallback>
                    {appointment.clientName
                      .split(" ")
                      .map((n) => n[0])
                      .join("")
                      .toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{appointment.clientName}</p>
                  <p className="text-sm text-muted-foreground">
                    {new Date(appointment.date).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })} -{" "}
                    {appointment.service}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge
                  variant={
                    appointment.status === "confirmed"
                      ? "default"
                      : appointment.status === "completed"
                        ? "success"
                        : appointment.status === "cancelled"
                          ? "destructive"
                          : "outline"
                  }
                >
                  {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                </Badge>
                <Button variant="ghost" size="sm">
                  View
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

