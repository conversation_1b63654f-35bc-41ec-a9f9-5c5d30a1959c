"use client"

import { useState, useEffect, useMemo } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { ChevronLeft, ChevronRight, FileDown, Plus } from "lucide-react"
import { format, addDays, subDays, isSameDay } from "date-fns"
import type { DateRange } from "react-day-picker"
import { useTransactions } from "@/lib/transaction-provider"
import { TransactionType, TransactionStatus, PaymentMethod } from "@/lib/transaction-types"

interface DailySalesProps {
  dateRange?: DateRange
  singleDate?: Date
  dateMode?: "single" | "range"
  selectedLocation?: string
}

interface TransactionSummaryItem {
  itemType: string
  salesQty: number
  refundQty: number
  grossTotal: number
}

interface CashMovementItem {
  paymentType: string
  paymentsCollected: number
  refundsPaid: number
}

export function DailySales({
  dateRange,
  singleDate,
  dateMode = "single",
  selectedLocation = "all"
}: DailySalesProps) {
  const { transactions, filterTransactions } = useTransactions()
  const [currentDate, setCurrentDate] = useState<Date>(singleDate || new Date())
  const [activeSection, setActiveSection] = useState("daily-sales-summary")

  // Get transactions for the current date
  const dailyTransactions = useMemo(() => {
    const filters: any = {
      singleDate: currentDate
    }

    if (selectedLocation !== "all") {
      filters.location = selectedLocation
    }

    return filterTransactions(filters)
  }, [currentDate, selectedLocation, filterTransactions])

  // Calculate transaction summary data
  const transactionSummary = useMemo(() => {
    const summary: TransactionSummaryItem[] = []
    const itemTypes = new Map<string, { sales: number, refunds: number, grossTotal: number }>()

    // Initialize with standard categories to match reference image
    const standardCategories = [
      "Services",
      "Products",
      "Shipping",
      "Gift cards",
      "Memberships",
      "Late cancellation fees",
      "No-show fees",
      "Refund amount"
    ]

    standardCategories.forEach(category => {
      itemTypes.set(category, { sales: 0, refunds: 0, grossTotal: 0 })
    })

    dailyTransactions.forEach(tx => {
      let itemType = "Services" // Default to services

      // Categorize based on transaction type and description
      if (tx.type === TransactionType.SERVICE_SALE || tx.category === "Service") {
        itemType = "Services"
      } else if (tx.type === TransactionType.PRODUCT_SALE || tx.category === "Product") {
        itemType = "Products"
      } else if (tx.type === TransactionType.GIFT_CARD_SALE) {
        itemType = "Gift cards"
      } else if (tx.type === TransactionType.MEMBERSHIP_SALE || tx.type === TransactionType.MEMBERSHIP_RENEWAL) {
        itemType = "Memberships"
      } else if (tx.description.toLowerCase().includes("shipping")) {
        itemType = "Shipping"
      } else if (tx.description.toLowerCase().includes("gift card")) {
        itemType = "Gift cards"
      } else if (tx.description.toLowerCase().includes("membership")) {
        itemType = "Memberships"
      } else if (tx.description.toLowerCase().includes("late cancellation")) {
        itemType = "Late cancellation fees"
      } else if (tx.description.toLowerCase().includes("no-show")) {
        itemType = "No-show fees"
      } else if (tx.type === TransactionType.REFUND) {
        itemType = "Refund amount"
      }

      const item = itemTypes.get(itemType)!

      if (tx.status === TransactionStatus.REFUNDED || tx.type === TransactionType.REFUND) {
        item.refunds += 1
        item.grossTotal -= Math.abs(tx.amount)
      } else if (tx.status === TransactionStatus.COMPLETED) {
        item.sales += 1
        item.grossTotal += tx.amount
      }
    })

    // Convert to array format, only include categories with data
    itemTypes.forEach((data, itemType) => {
      summary.push({
        itemType,
        salesQty: data.sales,
        refundQty: data.refunds,
        grossTotal: data.grossTotal
      })
    })

    // Add total row
    const totalSales = summary.reduce((sum, item) => sum + item.salesQty, 0)
    const totalRefunds = summary.reduce((sum, item) => sum + item.refundQty, 0)
    const totalGross = summary.reduce((sum, item) => sum + item.grossTotal, 0)

    summary.push({
      itemType: "Total Sales",
      salesQty: totalSales,
      refundQty: totalRefunds,
      grossTotal: totalGross
    })

    return summary
  }, [dailyTransactions])

  // Calculate cash movement summary data
  const cashMovementSummary = useMemo(() => {
    const summary: CashMovementItem[] = []
    const paymentTypes = new Map<string, { collected: number, refunded: number }>()

    // Initialize with updated payment types for better categorization
    const standardPaymentTypes = [
      "Cash",
      "Card Payment",
      "Mobile Payment",
      "Gift card redemptions",
      "Payments collected",
      "Of which tips"
    ]

    standardPaymentTypes.forEach(type => {
      paymentTypes.set(type, { collected: 0, refunded: 0 })
    })

    dailyTransactions.forEach(tx => {
      let paymentType = "Card Payment" // Default to Card Payment instead of Other

      switch (tx.paymentMethod) {
        case PaymentMethod.CASH:
          paymentType = "Cash"
          break
        case PaymentMethod.CREDIT_CARD:
        case PaymentMethod.BANK_TRANSFER:
        case PaymentMethod.CHECK:
          paymentType = "Card Payment"
          break
        case PaymentMethod.MOBILE_PAYMENT:
          paymentType = "Mobile Payment"
          break
        case PaymentMethod.GIFT_CARD:
          paymentType = "Gift card redemptions"
          break
        case PaymentMethod.LOYALTY_POINTS:
          paymentType = "Mobile Payment" // Loyalty points are typically mobile/app-based
          break
        case PaymentMethod.OTHER:
          paymentType = "Card Payment" // Migrate existing "Other" payments to Card Payment
          break
        default:
          paymentType = "Card Payment" // Default fallback to Card Payment
      }

      const payment = paymentTypes.get(paymentType)!

      if (tx.status === TransactionStatus.REFUNDED || tx.type === TransactionType.REFUND) {
        payment.refunded += Math.abs(tx.amount)
      } else if (tx.status === TransactionStatus.COMPLETED) {
        payment.collected += tx.amount

        // Add to "Payments collected" total
        const paymentsCollected = paymentTypes.get("Payments collected")!
        paymentsCollected.collected += tx.amount
      }
    })

    // Convert to array format
    paymentTypes.forEach((data, paymentType) => {
      summary.push({
        paymentType,
        paymentsCollected: data.collected,
        refundsPaid: data.refunded
      })
    })

    return summary
  }, [dailyTransactions])

  const handlePreviousDay = () => {
    setCurrentDate(prev => subDays(prev, 1))
  }

  const handleNextDay = () => {
    setCurrentDate(prev => addDays(prev, 1))
  }

  const handleToday = () => {
    setCurrentDate(new Date())
  }

  const isToday = isSameDay(currentDate, new Date())

  return (
    <div className="flex gap-6">
      {/* Left Sidebar Navigation */}
      <div className="w-56 space-y-1">
        <Card className="p-2">
          <div className="space-y-1">
            <Button
              variant={activeSection === "daily-sales-summary" ? "default" : "ghost"}
              className="w-full justify-start text-sm h-8"
              onClick={() => setActiveSection("daily-sales-summary")}
            >
              Daily sales summary
            </Button>
            <Button
              variant={activeSection === "appointments" ? "default" : "ghost"}
              className="w-full justify-start text-sm h-8"
              onClick={() => setActiveSection("appointments")}
            >
              Appointments
            </Button>
            <Button
              variant={activeSection === "sales" ? "default" : "ghost"}
              className="w-full justify-start text-sm h-8"
              onClick={() => setActiveSection("sales")}
            >
              Sales
            </Button>
            <Button
              variant={activeSection === "payments" ? "default" : "ghost"}
              className="w-full justify-start text-sm h-8"
              onClick={() => setActiveSection("payments")}
            >
              Payments
            </Button>
            <Button
              variant={activeSection === "gift-cards-sold" ? "default" : "ghost"}
              className="w-full justify-start text-sm h-8"
              onClick={() => setActiveSection("gift-cards-sold")}
            >
              Gift cards sold
            </Button>
            <Button
              variant={activeSection === "memberships-sold" ? "default" : "ghost"}
              className="w-full justify-start text-sm h-8"
              onClick={() => setActiveSection("memberships-sold")}
            >
              Memberships sold
            </Button>
          </div>
        </Card>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-6">
            <h2 className="text-xl font-semibold">Daily sales</h2>
            <p className="text-sm text-muted-foreground">View, filter and export the transactions and cash movement for the day.</p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <FileDown className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button size="sm">
              <Plus className="mr-2 h-4 w-4" />
              Add new
            </Button>
          </div>
        </div>

        {/* Date Navigation */}
        <div className="flex items-center gap-2 mb-6">
          <Button
            variant="outline"
            size="sm"
            onClick={handlePreviousDay}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant={isToday ? "default" : "outline"}
            size="sm"
            onClick={handleToday}
          >
            Today
          </Button>
          <span className="text-sm font-medium px-2">
            {format(currentDate, "EEEE d MMM, yyyy")}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={handleNextDay}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* Content based on active section */}
        {activeSection === "daily-sales-summary" && (
          <div className="grid grid-cols-2 gap-6">
            {/* Transaction Summary */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base font-medium">Transaction summary</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-muted/50">
                        <TableHead className="font-medium">Item type</TableHead>
                        <TableHead className="text-right font-medium">Sales qty</TableHead>
                        <TableHead className="text-right font-medium">Refund qty</TableHead>
                        <TableHead className="text-right font-medium">Gross total</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {transactionSummary.map((item, index) => (
                        <TableRow
                          key={index}
                          className={item.itemType === "Total Sales" ? "font-semibold border-t-2 bg-muted/30" : ""}
                        >
                          <TableCell className="py-2">{item.itemType}</TableCell>
                          <TableCell className="text-right py-2">{item.salesQty}</TableCell>
                          <TableCell className="text-right py-2">{item.refundQty}</TableCell>
                          <TableCell className="text-right py-2">
                            <CurrencyDisplay amount={item.grossTotal} />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>

            {/* Cash Movement Summary */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base font-medium">Cash movement summary</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-muted/50">
                        <TableHead className="font-medium">Payment type</TableHead>
                        <TableHead className="text-right font-medium">Payments collected</TableHead>
                        <TableHead className="text-right font-medium">Refunds paid</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {cashMovementSummary.map((item, index) => (
                        <TableRow key={index}>
                          <TableCell className="py-2">{item.paymentType}</TableCell>
                          <TableCell className="text-right py-2">
                            <CurrencyDisplay amount={item.paymentsCollected} />
                          </TableCell>
                          <TableCell className="text-right py-2">
                            <CurrencyDisplay amount={item.refundsPaid} />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Other sections content */}
        {activeSection === "appointments" && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base font-medium">Appointments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-muted/50">
                      <TableHead className="font-medium">Time</TableHead>
                      <TableHead className="font-medium">Client</TableHead>
                      <TableHead className="font-medium">Service</TableHead>
                      <TableHead className="text-right font-medium">Amount</TableHead>
                      <TableHead className="font-medium">Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {dailyTransactions
                      .filter(tx => tx.reference?.type === 'appointment')
                      .map((tx, index) => (
                        <TableRow key={index}>
                          <TableCell className="py-2">{format(new Date(tx.date), 'HH:mm')}</TableCell>
                          <TableCell className="py-2">{tx.clientName || 'N/A'}</TableCell>
                          <TableCell className="py-2">{tx.description}</TableCell>
                          <TableCell className="text-right py-2">
                            <CurrencyDisplay amount={tx.amount} />
                          </TableCell>
                          <TableCell className="py-2">{tx.status}</TableCell>
                        </TableRow>
                      ))}
                    {dailyTransactions.filter(tx => tx.reference?.type === 'appointment').length === 0 && (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-4 text-muted-foreground">
                          No appointments found for this date.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        )}

        {activeSection === "sales" && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base font-medium">Sales</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-muted/50">
                      <TableHead className="font-medium">Time</TableHead>
                      <TableHead className="font-medium">Item</TableHead>
                      <TableHead className="font-medium">Quantity</TableHead>
                      <TableHead className="text-right font-medium">Amount</TableHead>
                      <TableHead className="font-medium">Payment Method</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {dailyTransactions
                      .filter(tx => tx.type === TransactionType.PRODUCT_SALE || tx.type === TransactionType.SERVICE_SALE)
                      .map((tx, index) => (
                        <TableRow key={index}>
                          <TableCell className="py-2">{format(new Date(tx.date), 'HH:mm')}</TableCell>
                          <TableCell className="py-2">{tx.description}</TableCell>
                          <TableCell className="py-2">{tx.quantity || 1}</TableCell>
                          <TableCell className="text-right py-2">
                            <CurrencyDisplay amount={tx.amount} />
                          </TableCell>
                          <TableCell className="py-2">{tx.paymentMethod}</TableCell>
                        </TableRow>
                      ))}
                    {dailyTransactions.filter(tx => tx.type === TransactionType.PRODUCT_SALE || tx.type === TransactionType.SERVICE_SALE).length === 0 && (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-4 text-muted-foreground">
                          No sales found for this date.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        )}

        {activeSection === "payments" && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base font-medium">Payments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-muted/50">
                      <TableHead className="font-medium">Time</TableHead>
                      <TableHead className="font-medium">Client</TableHead>
                      <TableHead className="font-medium">Payment Method</TableHead>
                      <TableHead className="text-right font-medium">Amount</TableHead>
                      <TableHead className="font-medium">Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {dailyTransactions
                      .filter(tx => tx.status === TransactionStatus.COMPLETED)
                      .map((tx, index) => (
                        <TableRow key={index}>
                          <TableCell className="py-2">{format(new Date(tx.date), 'HH:mm')}</TableCell>
                          <TableCell className="py-2">{tx.clientName || 'N/A'}</TableCell>
                          <TableCell className="py-2">{tx.paymentMethod}</TableCell>
                          <TableCell className="text-right py-2">
                            <CurrencyDisplay amount={tx.amount} />
                          </TableCell>
                          <TableCell className="py-2">{tx.status}</TableCell>
                        </TableRow>
                      ))}
                    {dailyTransactions.filter(tx => tx.status === TransactionStatus.COMPLETED).length === 0 && (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-4 text-muted-foreground">
                          No payments found for this date.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        )}

        {activeSection === "gift-cards-sold" && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base font-medium">Gift Cards Sold</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-muted/50">
                      <TableHead className="font-medium">Time</TableHead>
                      <TableHead className="font-medium">Client</TableHead>
                      <TableHead className="font-medium">Gift Card Code</TableHead>
                      <TableHead className="text-right font-medium">Amount</TableHead>
                      <TableHead className="font-medium">Payment Method</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {dailyTransactions
                      .filter(tx => tx.type === TransactionType.GIFT_CARD_SALE)
                      .map((tx, index) => (
                        <TableRow key={index}>
                          <TableCell className="py-2">{format(new Date(tx.date), 'HH:mm')}</TableCell>
                          <TableCell className="py-2">{tx.clientName || 'N/A'}</TableCell>
                          <TableCell className="py-2">{tx.metadata?.giftCardCode || 'N/A'}</TableCell>
                          <TableCell className="text-right py-2">
                            <CurrencyDisplay amount={tx.amount} />
                          </TableCell>
                          <TableCell className="py-2">{tx.paymentMethod}</TableCell>
                        </TableRow>
                      ))}
                    {dailyTransactions.filter(tx => tx.type === TransactionType.GIFT_CARD_SALE).length === 0 && (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-4 text-muted-foreground">
                          No gift cards sold for this date.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        )}

        {activeSection === "memberships-sold" && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base font-medium">Memberships Sold</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-muted/50">
                      <TableHead className="font-medium">Time</TableHead>
                      <TableHead className="font-medium">Client</TableHead>
                      <TableHead className="font-medium">Membership Tier</TableHead>
                      <TableHead className="text-right font-medium">Amount</TableHead>
                      <TableHead className="font-medium">Payment Method</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {dailyTransactions
                      .filter(tx => tx.type === TransactionType.MEMBERSHIP_SALE || tx.type === TransactionType.MEMBERSHIP_RENEWAL)
                      .map((tx, index) => (
                        <TableRow key={index}>
                          <TableCell className="py-2">{format(new Date(tx.date), 'HH:mm')}</TableCell>
                          <TableCell className="py-2">{tx.clientName || 'N/A'}</TableCell>
                          <TableCell className="py-2">{tx.metadata?.membershipTier || 'N/A'}</TableCell>
                          <TableCell className="text-right py-2">
                            <CurrencyDisplay amount={tx.amount} />
                          </TableCell>
                          <TableCell className="py-2">{tx.paymentMethod}</TableCell>
                        </TableRow>
                      ))}
                    {dailyTransactions.filter(tx => tx.type === TransactionType.MEMBERSHIP_SALE || tx.type === TransactionType.MEMBERSHIP_RENEWAL).length === 0 && (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-4 text-muted-foreground">
                          No memberships sold for this date.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
