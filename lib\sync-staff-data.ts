'use client';

/**
 * Utility to sync staff data from FileStaffStorage API to localStorage
 * This ensures client-side components get the latest data from the server
 */

export async function syncStaffFromAPI(): Promise<boolean> {
  try {
    console.log('syncStaffFromAPI: Fetching staff data from FileStaffStorage API...');
    
    const response = await fetch('/api/staff');
    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`);
    }
    
    const data = await response.json();
    console.log('syncStaffFromAPI: Received', data.staff.length, 'staff members from API');
    
    // Update localStorage with the latest data
    if (typeof window !== 'undefined') {
      localStorage.setItem('vanity_staff', JSON.stringify(data.staff));
      console.log('syncStaffFromAPI: Successfully synced staff data to localStorage');
      
      // Don't dispatch staff-updated event to prevent infinite loops
      // window.dispatchEvent(new CustomEvent('staff-updated', {
      //   detail: { staff: data.staff }
      // }));
      
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('syncStaffFromAPI: Error syncing staff data:', error);
    return false;
  }
}

/**
 * Initialize staff data sync on app startup
 */
export function initializeStaffSync(): void {
  if (typeof window !== 'undefined') {
    // Sync immediately on load
    syncStaffFromAPI();
    
    // Set up periodic sync every 5 minutes to keep data fresh
    setInterval(syncStaffFromAPI, 5 * 60 * 1000);
    
    console.log('initializeStaffSync: Staff data sync initialized');
  }
}
