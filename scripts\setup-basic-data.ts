#!/usr/bin/env tsx

import { prisma } from '../lib/prisma'

async function setupBasicData() {
  console.log('🚀 Setting up basic data...')

  try {
    // Create the 5 required locations
    const locations = [
      { id: 'loc1', name: 'D-ring road', address: 'D-ring road, Doha', city: 'Doha', country: 'Qatar' },
      { id: 'loc2', name: '<PERSON><PERSON><PERSON>', address: 'Muaither, Doha', city: 'Doha', country: 'Qatar' },
      { id: 'loc3', name: 'Medinat Khalifa', address: 'Medinat Khalifa, Doha', city: 'Doha', country: 'Qatar' },
      { id: 'home', name: 'Home service', address: 'Mobile Service', city: 'Doha', country: 'Qatar' },
      { id: 'online', name: 'Online store', address: 'Online Platform', city: 'Doha', country: 'Qatar' }
    ]

    console.log('📍 Creating locations...')
    for (const locationData of locations) {
      try {
        await prisma.location.upsert({
          where: { id: locationData.id },
          update: {},
          create: {
            id: locationData.id,
            name: locationData.name,
            address: locationData.address,
            city: locationData.city,
            country: locationData.country,
            isActive: true
          }
        })
        console.log(`   ✅ Created/Updated location: ${locationData.name}`)
      } catch (error) {
        console.log(`   ⚠️ Location ${locationData.name} already exists or error: ${error}`)
      }
    }

    // Create service categories
    const categories = [
      { id: 'braiding', name: 'Braiding' },
      { id: 'hair-extension', name: 'Hair Extension' },
      { id: 'styling', name: 'Styling' },
      { id: 'hair-treatment', name: 'Hair Treatment' },
      { id: 'color', name: 'Color' },
      { id: 'nail', name: 'Nail' },
      { id: 'eyelash', name: 'Eyelash' },
      { id: 'threading', name: 'Threading' },
      { id: 'waxing', name: 'Waxing' },
      { id: 'henna', name: 'Henna' },
      { id: 'massage-and-spa', name: 'Massage And Spa' }
    ]

    console.log('📋 Creating service categories...')
    for (const category of categories) {
      try {
        await prisma.serviceCategory.upsert({
          where: { id: category.id },
          update: {},
          create: {
            id: category.id,
            name: category.name,
            isActive: true
          }
        })
        console.log(`   ✅ Created/Updated category: ${category.name}`)
      } catch (error) {
        console.log(`   ⚠️ Category ${category.name} already exists or error: ${error}`)
      }
    }

    // Create a few sample services
    const sampleServices = [
      { name: 'Basic Haircut', category: 'styling', duration: 60, price: 150 },
      { name: 'Hair Coloring', category: 'color', duration: 120, price: 300 },
      { name: 'Manicure', category: 'nail', duration: 45, price: 80 },
      { name: 'Pedicure', category: 'nail', duration: 60, price: 100 },
      { name: 'Facial Treatment', category: 'massage-and-spa', duration: 90, price: 200 }
    ]

    console.log('💅 Creating sample services...')
    for (const service of sampleServices) {
      try {
        const createdService = await prisma.service.create({
          data: {
            name: service.name,
            category: service.category,
            duration: service.duration,
            price: service.price,
            isActive: true
          }
        })

        // Link service to all locations
        for (const location of locations) {
          try {
            await prisma.locationService.create({
              data: {
                serviceId: createdService.id,
                locationId: location.id,
                price: service.price,
                isActive: true
              }
            })
          } catch (error) {
            // Relationship might already exist
          }
        }

        console.log(`   ✅ Created service: ${service.name}`)
      } catch (error) {
        console.log(`   ⚠️ Service ${service.name} already exists or error: ${error}`)
      }
    }

    console.log('✅ Basic data setup completed!')

    // Verify the setup
    const locationCount = await prisma.location.count({ where: { isActive: true } })
    const categoryCount = await prisma.serviceCategory.count({ where: { isActive: true } })
    const serviceCount = await prisma.service.count({ where: { isActive: true } })
    const relationshipCount = await prisma.locationService.count({ where: { isActive: true } })

    console.log('\n📊 Setup Summary:')
    console.log(`   Locations: ${locationCount}`)
    console.log(`   Categories: ${categoryCount}`)
    console.log(`   Services: ${serviceCount}`)
    console.log(`   Service-Location Relationships: ${relationshipCount}`)

  } catch (error) {
    console.error('❌ Error setting up basic data:', error)
  } finally {
    await prisma.$disconnect()
  }
}

setupBasicData().catch(console.error)
