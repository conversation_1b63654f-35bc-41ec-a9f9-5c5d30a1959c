#!/usr/bin/env tsx

import { prisma } from '../lib/prisma'

async function cleanupDuplicateLocations() {
  console.log('🧹 Cleaning up duplicate location data...')

  try {
    // Define the target locations we want to keep
    const targetLocations = ['loc1', 'loc2', 'loc3', 'online']

    // Use a database transaction to ensure atomicity
    const result = await prisma.$transaction(async (tx) => {
      // 1. Get all products
      const products = await tx.product.findMany({
        where: { isActive: true },
        select: { id: true, name: true }
      })

      console.log(`📦 Found ${products.length} active products`)

      // 2. Get all product locations that are NOT in our target locations
      const oldProductLocations = await tx.productLocation.findMany({
        where: {
          locationId: { notIn: targetLocations }
        },
        include: {
          product: { select: { name: true } },
          location: { select: { name: true } }
        }
      })

      console.log(`🗑️ Found ${oldProductLocations.length} old product location records to delete`)

      // 3. Delete old product location records
      const deleteResult = await tx.productLocation.deleteMany({
        where: {
          locationId: { notIn: targetLocations }
        }
      })

      console.log(`✅ Deleted ${deleteResult.count} old product location records`)

      // 4. Verify current stock levels
      const currentProductLocations = await tx.productLocation.findMany({
        where: {
          locationId: { in: targetLocations }
        },
        include: {
          product: { select: { name: true } },
          location: { select: { name: true } }
        }
      })

      console.log(`📊 Current product location records: ${currentProductLocations.length}`)

      // Group by product to show stock summary
      const productStockSummary = new Map()
      
      for (const pl of currentProductLocations) {
        const productName = pl.product.name
        if (!productStockSummary.has(productName)) {
          productStockSummary.set(productName, { totalStock: 0, locations: [] })
        }
        
        const summary = productStockSummary.get(productName)
        summary.totalStock += pl.stock
        summary.locations.push({
          locationName: pl.location.name,
          stock: pl.stock
        })
      }

      return {
        productsProcessed: products.length,
        oldRecordsDeleted: deleteResult.count,
        currentRecords: currentProductLocations.length,
        productStockSummary: Array.from(productStockSummary.entries()).map(([name, data]) => ({
          productName: name,
          totalStock: data.totalStock,
          locations: data.locations
        }))
      }
    })

    console.log(`\n🎉 SUCCESS! Cleanup completed:`)
    console.log(`   📦 Products processed: ${result.productsProcessed}`)
    console.log(`   🗑️ Old records deleted: ${result.oldRecordsDeleted}`)
    console.log(`   📊 Current records: ${result.currentRecords}`)
    
    console.log(`\n📊 Stock Summary by Product:`)
    for (const product of result.productStockSummary) {
      console.log(`   ${product.productName}: Total ${product.totalStock} units`)
      for (const location of product.locations) {
        console.log(`     - ${location.locationName}: ${location.stock}`)
      }
    }

  } catch (error) {
    console.error('❌ Error cleaning up duplicate locations:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the script
cleanupDuplicateLocations()
