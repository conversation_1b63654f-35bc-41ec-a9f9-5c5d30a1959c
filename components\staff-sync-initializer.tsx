'use client';

import { useEffect } from 'react';
import { initializeStaffSync } from '@/lib/sync-staff-data';

/**
 * Component to initialize staff data synchronization from FileStaffStorage API
 * This ensures client-side components always have the latest staff data
 */
export function StaffSyncInitializer() {
  useEffect(() => {
    // Initialize staff data sync on component mount
    initializeStaffSync();
  }, []);

  // This component doesn't render anything
  return null;
}
