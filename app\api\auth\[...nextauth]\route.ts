import NextAuth from "next-auth"
import Cred<PERSON><PERSON><PERSON>rovider from "next-auth/providers/credentials"
import { prisma } from "@/lib/prisma"
import bcrypt from "bcryptjs"

// Enhanced password comparison using bcrypt
const comparePasswords = async (plainPassword: string, hashedPassword: string) => {
  try {
    // Use bcrypt for proper password comparison
    return await bcrypt.compare(plainPassword, hashedPassword)
  } catch (error) {
    console.error("Password comparison error:", error)
    return false
  }
}

const handler = NextAuth({
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        try {
          // Use Prisma to find user with staff profile and locations
          const user = await prisma.user.findUnique({
            where: { email: credentials.email },
            include: {
              staffProfile: {
                include: {
                  locations: {
                    include: {
                      location: true
                    }
                  }
                }
              }
            }
          })

          if (!user || !user.isActive) {
            return null
          }

          const passwordMatch = await comparePasswords(credentials.password, user.password)

          if (!passwordMatch) {
            return null
          }

          // Get user locations from staff profile
          let locationIds: string[] = []
          if (user.staffProfile?.locations) {
            locationIds = user.staffProfile.locations
              .filter(sl => sl.isActive)
              .map(sl => sl.location.id)
          }

          return {
            id: user.id,
            name: user.staffProfile?.name || user.email.split('@')[0],
            email: user.email,
            role: user.role,
            locations: user.role === "ADMIN" ? ["all"] : locationIds,
          }
        } catch (error) {
          console.error("Auth error:", error)
          return null
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id
        token.role = user.role
        token.locations = user.locations
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string
        session.user.role = token.role as string
        session.user.locations = token.locations as string[]
      }
      return session
    },
  },
  pages: {
    signIn: "/login",
    error: "/login",
  },
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  secret: process.env.NEXTAUTH_SECRET,
})

export { handler as GET, handler as POST }

