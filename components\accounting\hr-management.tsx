"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { useStaff } from "@/lib/use-staff-data"
import { TimeOffManagement } from "@/components/hr/time-off-management"
import { BenefitsManagement } from "@/components/hr/benefits-management"
import { PerformanceReviews } from "@/components/hr/performance-reviews"
import { TrainingManagement } from "@/components/hr/training-management"
import { DocumentManagement } from "@/components/hr/document-management"
import { DocumentExpiryCounter } from "@/components/hr/document-expiry-counter"
import { OnboardingOffboarding } from "@/components/hr/onboarding-offboarding"
import { HRPayrollManagement } from "@/components/hr/hr-payroll-management"
import { StaffDirectory } from "@/components/staff/staff-directory"
import { HRStaffManagement } from "@/components/hr/hr-staff-management"
import { StaffSeeding } from "@/components/staff-seeding"
import { Button } from "@/components/ui/button"
import { useRouter, useSearchParams } from "next/navigation"
import { ExternalLink } from "lucide-react"
import { useCurrency } from "@/lib/currency-provider"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { NotificationService } from "@/lib/notification-service"

interface HRManagementProps {
  search: string
}

export function HRManagement({ search }: HRManagementProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const tabParam = searchParams.get('tab')
  const [activeTab, setActiveTab] = useState(tabParam || "overview")
  const { formatCurrency } = useCurrency()

  // Check for document notifications on component mount
  useEffect(() => {
    // Check for expiring documents and create notifications
    NotificationService.checkExpiringDocuments()
  }, [])

  // Listen for currency changes to ensure consistent currency display
  useEffect(() => {
    const handleCurrencyChange = () => {
      // Force a re-render when currency changes
      setActiveTab(prev => prev)
    }

    document.addEventListener('currency-changed', handleCurrencyChange)
    return () => {
      document.removeEventListener('currency-changed', handleCurrencyChange)
    }
  }, [])

  // Update URL when tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value)

    // Check if we're already on the HR page
    const isHRPage = window.location.pathname.includes('/dashboard/hr')

    if (isHRPage) {
      // Just update the tab parameter
      router.push(`/dashboard/hr?tab=${value}`, { scroll: false })
    } else {
      // We're on a different page (like accounting), so we need to preserve the current URL
      // and just update the component state
      console.log(`Tab changed to ${value} but not navigating since we're not on the HR page`)
    }
  }

  const handleStaffUpdated = (updatedStaff: any) => {
    console.log("Staff updated from HR Management:", updatedStaff)
  }

  const handleStaffDeleted = (staffId: string) => {
    console.log("Staff deleted from HR Management:", staffId)
  }

  const navigateToStaffPage = () => {
    router.push("/dashboard/staff")
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Human Resources Management</h3>
        <Button variant="outline" onClick={navigateToStaffPage}>
          <ExternalLink className="mr-2 h-4 w-4" />
          Go to Staff Management
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="staff">Staff Management</TabsTrigger>
          <TabsTrigger value="payroll">Payroll</TabsTrigger>
          <TabsTrigger value="time-off">Time Off</TabsTrigger>
          <TabsTrigger value="benefits">Benefits</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="training">Training</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="onboarding">Onboarding</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Time Off Requests</CardTitle>
                <CardDescription>Pending approval</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">5</div>
                <p className="text-xs text-muted-foreground">
                  +2 from last month
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Staff Utilization</CardTitle>
                <CardDescription>Average across all staff</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">87%</div>
                <p className="text-xs text-muted-foreground">
                  +5% from last month
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Training Completion</CardTitle>
                <CardDescription>Required trainings</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">92%</div>
                <p className="text-xs text-muted-foreground">
                  +3% from last month
                </p>
              </CardContent>
            </Card>
            <DocumentExpiryCounter />
          </div>

          <div className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Staff Directory</CardTitle>
                <CardDescription>
                  View and manage your staff members
                </CardDescription>
              </CardHeader>
              <CardContent>
                <StaffDirectory
                  search={search}
                  onStaffUpdated={handleStaffUpdated}
                  onStaffDeleted={handleStaffDeleted}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="staff">
          <div className="space-y-6">
            <StaffSeeding />
            <HRStaffManagement search={search} />
          </div>
        </TabsContent>

        <TabsContent value="payroll">
          <HRPayrollManagement />
        </TabsContent>

        <TabsContent value="time-off">
          <TimeOffManagement />
        </TabsContent>

        <TabsContent value="benefits">
          <BenefitsManagement />
        </TabsContent>

        <TabsContent value="performance">
          <PerformanceReviews />
        </TabsContent>

        <TabsContent value="training">
          <TrainingManagement />
        </TabsContent>

        <TabsContent value="documents">
          <DocumentManagement />
        </TabsContent>

        <TabsContent value="onboarding">
          <OnboardingOffboarding />
        </TabsContent>
      </Tabs>
    </div>
  )
}
