import { withAuth } from "next-auth/middleware"
import { NextResponse } from "next/server"

export default withAuth(
  function middleware(req) {
    const { pathname } = req.nextUrl
    const token = req.nextauth.token

    // Role-based routing after successful authentication
    if (pathname === '/dashboard' && token?.role) {
      const role = token.role as string

      // Redirect staff and receptionists to appointments page
      if (role === 'STAFF' || role === 'RECEPTIONIST') {
        const appointmentsUrl = new URL('/dashboard/appointments', req.url)
        return NextResponse.redirect(appointmentsUrl)
      }
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl

        // Allow all API routes (they handle their own auth if needed)
        if (pathname.startsWith('/api')) {
          return true
        }

        // Allow login page
        if (pathname.startsWith('/login')) {
          return true
        }

        // Allow public pages
        if (pathname === '/' || pathname.startsWith('/client-portal') || pathname.startsWith('/booking')) {
          return true
        }

        // For dashboard routes, require authentication
        if (pathname.startsWith('/dashboard')) {
          return !!token
        }

        // Default: allow access
        return true
      },
    },
  }
)

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|public|images).*)',
  ],
}
