import { with<PERSON><PERSON> } from "next-auth/middleware"
import { NextResponse } from "next/server"

export default withAuth(
  function middleware(req) {
    const { pathname } = req.nextUrl
    const token = req.nextauth.token

    // Allow access to login page and API routes
    if (pathname.startsWith('/login') || pathname.startsWith('/api/auth')) {
      return NextResponse.next()
    }

    // Redirect to login if not authenticated
    if (!token) {
      const loginUrl = new URL('/login', req.url)
      return NextResponse.redirect(loginUrl)
    }

    // Role-based routing after successful authentication
    if (pathname === '/dashboard' && token.role) {
      const role = token.role as string
      
      // Redirect staff and receptionists to appointments page
      if (role === 'STAFF' || role === 'RECEPTIONIST') {
        const appointmentsUrl = new URL('/dashboard/appointments', req.url)
        return NextResponse.redirect(appointmentsUrl)
      }
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl
        
        // Always allow access to login and auth API routes
        if (pathname.startsWith('/login') || pathname.startsWith('/api/auth')) {
          return true
        }
        
        // For all other protected routes, require a token
        return !!token
      },
    },
  }
)

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (NextAuth API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api/auth|_next/static|_next/image|favicon.ico|public|images).*)',
  ],
}
