"use client"

import { useState } from "react"
import { useAuth } from "@/lib/auth-provider"
import { useStaff } from "@/lib/staff-provider"
import { getFirstName } from "@/lib/female-avatars"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Calendar } from "@/components/ui/calendar"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { format, parseISO, isWithinInterval, startOfDay, endOfDay } from "date-fns"
import { getAllAppointments } from "@/lib/appointment-service"

export function StaffAvailability() {
  const { currentLocation } = useAuth()
  const { staff, getStaffByLocation } = useStaff()
  const [date, setDate] = useState<Date>(new Date())
  const [selectedStaff, setSelectedStaff] = useState<string>("all")

  // Get REAL staff for the current location from HR system - NO mock data
  const filteredStaff = getStaffByLocation(currentLocation)

  // Debug log to verify we're using REAL staff data from HR system
  console.log("StaffAvailability - Using REAL staff from HR system");
  console.log("StaffAvailability - Current Location:", currentLocation);
  console.log("StaffAvailability - Total Real Staff Count:", staff.length);
  console.log("StaffAvailability - Filtered Staff for Location:", filteredStaff.length);

  // Verify we have real staff data (should be exactly 7 real staff members)
  if (staff.length === 0) {
    console.warn("⚠️ StaffAvailability - No staff data found! Check HR staff management system.");
  } else if (staff.length !== 7) {
    console.warn(`⚠️ StaffAvailability - Expected 7 real staff members, found ${staff.length}. Check HR system.`);
  } else {
    console.log("✅ StaffAvailability - Using correct number of real staff members (7)");
  }

  // Create time slots for the day
  const timeSlots = []
  for (let i = 9; i < 19; i++) {
    timeSlots.push({
      time: `${i}:00`,
      hour: i,
      minutes: 0,
    })
    timeSlots.push({
      time: `${i}:30`,
      hour: i,
      minutes: 30,
    })
  }

  // Filter REAL appointments for the selected day and staff - NO mock data
  const getAppointmentsForStaff = (staffId: string) => {
    // Get all real appointments from the appointment service
    const allAppointments = getAllAppointments();

    return allAppointments.filter((appointment) => {
      const appointmentDate = parseISO(appointment.date)

      // Check if it's within the selected day
      const isSelectedDay = isWithinInterval(appointmentDate, {
        start: startOfDay(date),
        end: endOfDay(date),
      })

      // Filter by staff if needed
      const isCorrectStaff = staffId === "all" || appointment.staffId === staffId

      // Filter by location
      const isCorrectLocation = currentLocation === "all" || appointment.location === currentLocation

      return isSelectedDay && isCorrectStaff && isCorrectLocation
    })
  }

  // Check if a staff member is available at a given time slot
  const isAvailable = (staffId: string, timeSlot: any) => {
    const appointments = getAppointmentsForStaff(staffId)

    for (const appointment of appointments) {
      // IMPORTANT: Skip completed appointments - they don't block staff availability
      if (appointment.status === "completed") {
        continue;
      }

      // IMPORTANT: Skip cancelled and no-show appointments - they don't block staff availability
      if (appointment.status === "cancelled" || appointment.status === "no-show") {
        continue;
      }

      const appointmentDate = parseISO(appointment.date)
      const appointmentHour = appointmentDate.getHours()
      const appointmentMinutes = appointmentDate.getMinutes()

      // Find appointment end time (add duration)
      const appointmentEndDate = new Date(appointmentDate.getTime() + appointment.duration * 60000)
      const appointmentEndHour = appointmentEndDate.getHours()
      const appointmentEndMinutes = appointmentEndDate.getMinutes()

      // Check if time slot is within appointment time
      const slotTime = timeSlot.hour + timeSlot.minutes / 60
      const appointmentStartTime = appointmentHour + appointmentMinutes / 60
      const appointmentEndTime = appointmentEndHour + appointmentEndMinutes / 60

      // If this is a blocked time entry, mark as unavailable
      if (appointment.type === "blocked" && slotTime >= appointmentStartTime && slotTime < appointmentEndTime) {
        return false
      }

      // For regular appointments, check if time slot is within appointment time
      if (slotTime >= appointmentStartTime && slotTime < appointmentEndTime) {
        return false
      }
    }

    return true
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Staff Availability</CardTitle>
        <CardDescription>Check availability for stylists across time slots</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-[300px_1fr] gap-6">
          <div>
            <Calendar
              mode="single"
              selected={date}
              onSelect={(newDate) => newDate && setDate(newDate)}
              className="rounded-md border"
            />

            <div className="mt-4">
              <Tabs defaultValue="all" value={selectedStaff} onValueChange={setSelectedStaff}>
                <TabsList className="w-full flex-wrap">
                  <TabsTrigger value="all">All Staff</TabsTrigger>
                  {filteredStaff.map((staff) => (
                    <TabsTrigger key={staff.id} value={staff.id}>
                      {getFirstName(staff.name)}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </Tabs>
            </div>
          </div>

          <div className="overflow-auto">
            <h3 className="font-medium text-lg mb-4">{format(date, "EEEE, MMMM d, yyyy")}</h3>

            <div className="border rounded-md">
              <div className="grid grid-cols-[100px_1fr] overflow-x-auto">
                <div></div>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 border-b">
                  {selectedStaff === "all" ? (
                    filteredStaff.map((staff) => (
                      <div key={staff.id} className="p-2 text-center font-medium border-r last:border-r-0">
                        {getFirstName(staff.name)}
                      </div>
                    ))
                  ) : (
                    <div className="p-2 text-center font-medium">
                      {getFirstName(filteredStaff.find((s) => s.id === selectedStaff)?.name || "Unknown Staff")}
                    </div>
                  )}
                </div>

                {timeSlots.map((timeSlot) => (
                  <div key={timeSlot.time} className="grid grid-cols-[100px_1fr] border-b last:border-b-0">
                    <div className="p-2 border-r text-sm font-medium text-muted-foreground">{timeSlot.time}</div>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                      {selectedStaff === "all" ? (
                        filteredStaff.map((staff) => (
                          <div
                            key={staff.id}
                            className={`p-2 text-center border-r last:border-r-0 ${
                              isAvailable(staff.id, timeSlot)
                                ? "bg-green-50 dark:bg-green-950"
                                : "bg-red-50 dark:bg-red-950"
                            }`}
                          >
                            {isAvailable(staff.id, timeSlot) ? "Available" : "Booked"}
                          </div>
                        ))
                      ) : (
                        <div
                          className={`p-2 text-center ${
                            isAvailable(selectedStaff, timeSlot)
                              ? "bg-green-50 dark:bg-green-950"
                              : "bg-red-50 dark:bg-red-950"
                          }`}
                        >
                          {isAvailable(selectedStaff, timeSlot) ? "Available" : "Booked"}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

