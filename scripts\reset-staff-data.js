// Script to reset staff data to exactly 7 real staff members
// Run this in browser console: node scripts/reset-staff-data.js

console.log('🔄 Resetting staff data to exactly 7 real staff members...');

// Clear localStorage staff data
if (typeof window !== 'undefined') {
  localStorage.removeItem('vanity_staff');
  console.log('✅ Cleared staff data from localStorage');
  
  // Dispatch event to trigger staff provider refresh
  window.dispatchEvent(new CustomEvent('staff-updated'));
  console.log('✅ Dispatched staff-updated event');
  
  // Force page reload to reinitialize with correct staff data
  setTimeout(() => {
    console.log('🔄 Reloading page to reinitialize staff data...');
    window.location.reload();
  }, 1000);
} else {
  console.log('❌ Not running in browser environment');
}

console.log('✅ Staff data reset initiated - page will reload with exactly 7 real staff members');
